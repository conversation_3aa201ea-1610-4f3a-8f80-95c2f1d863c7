<template>
  <ConfigProvider :theme-vars="themeVars">
    <DefaultLayout> <RouterView /> </DefaultLayout>
  </ConfigProvider>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import DefaultLayout from './layout/default.vue'

import { ConfigProvider } from 'vant'
import type { ConfigProviderThemeVars } from 'vant'

const themeVars: ConfigProviderThemeVars = {
  primaryColor: '#EF383C',
  tabsBottomBarColor: '#EF383C',
}
</script>

<style scoped lang="scss">
//
</style>
