import { createRouter, createWebHistory } from 'vue-router'
import Loan from '../views/loan/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'repayment',
      component: Loan,
      meta: {
        title: '我的贷款',
      },
    },
    // {
    //   path: '/limit',
    //   name: 'limit',
    //   //路由级代码拆分
    //   //这将为此路由生成一个单独的块（xx.[hash].js）
    //   //当访问该路由时，它会被动态导入
    //   component: () => import('../views/limit/index.vue'),
    //   meta: {
    //     title: '我的额度',
    //   },
    // },
  ],
})

router.beforeEach((to, from, next) => {
  document.title = to.meta.title
  next()
})

export default router
