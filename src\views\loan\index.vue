<template>
  <div class="">
    <Tabs v-model:active="activeTab" sticky>
      <Tab title="我的还款" name="repayment">
        <Repayment />
      </Tab>
      <Tab title="我的额度" name="limit">
        <Limit />
      </Tab>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Tab, Tabs } from 'vant'
import Repayment from './components/repayment.vue'
import Limit from './components/limit.vue'

const activeTab = ref('repayment')
</script>

<style scoped lang="scss">
//
</style>
